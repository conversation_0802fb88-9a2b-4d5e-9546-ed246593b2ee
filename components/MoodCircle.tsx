import React, { memo, useMemo, useEffect, useRef } from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Animated,
} from 'react-native';
import { useRouter } from 'expo-router';
import { moods, setGlobalSelectedMood } from '../appGlobalData';



const RADIUS = 130;
const IMAGE_SIZE = 70;
const CONTAINER_SIZE = 2 * (RADIUS + IMAGE_SIZE / 2);



const MoodCircle = memo(({ showMoodPicker }: { showMoodPicker: boolean }) => {
    const router = useRouter();

    const handlePress = (moodIndex: number) => {
        const selectedMood = moods[moodIndex];
        console.log('-- 选择的心情:', selectedMood.name, selectedMood.image);

        // 使用全局函数设置选中的心情信息
        setGlobalSelectedMood(moodIndex, selectedMood.name);

        // 直接跳转到编辑页面，不需要传递参数
        router.push('/editnote');
    };

    const positions = useMemo(() => {
        const centerX = CONTAINER_SIZE / 2;
        const centerY = CONTAINER_SIZE / 2;

        return moods.map((_, index) => {
            const angle = (2 * Math.PI * index) / moods.length;
            const x = centerX + RADIUS * Math.cos(angle) - IMAGE_SIZE / 2;
            const y = centerY + RADIUS * Math.sin(angle) - IMAGE_SIZE / 2;
            return { x, y };
        });
    }, []);

    // 每个图标有自己的 opacity / scale / rotate 动画值
    const animatedValues = useRef(
        moods.map(() => ({
            opacity: new Animated.Value(0),
            scale: new Animated.Value(0),
            rotate: new Animated.Value(0),
        }))
    ).current;

    useEffect(() => {
        if (showMoodPicker) {
            const animations = animatedValues.map((val, index) => {
                return Animated.parallel([
                    Animated.timing(val.opacity, {
                        toValue: 1,
                        duration: 300,
                        delay: index * 100,
                        useNativeDriver: true,
                    }),
                    Animated.timing(val.scale, {
                        toValue: 1,
                        duration: 300,
                        delay: index * 100,
                        useNativeDriver: true,
                    }),
                    Animated.timing(val.rotate, {
                        toValue: 1,
                        duration: 500,
                        delay: index * 100,
                        useNativeDriver: true,
                    }),
                ]);
            });

            Animated.stagger(100, animations).start();
        } else {
            // reset animation values
            animatedValues.forEach(val => {
                val.opacity.setValue(0);
                val.scale.setValue(0);
                val.rotate.setValue(0);
            });
        }
    }, [showMoodPicker]);

    if (!showMoodPicker) return null;

    return (
        <View style={styles.mood_picker_view}>
            {moods.map((mood, index) => {
                const { opacity, scale, rotate } = animatedValues[index];

                const rotateInterpolate = rotate.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '360deg'],
                });

                const animatedStyle = {
                    opacity,
                    transform: [
                        { scale },
                        { rotate: rotateInterpolate },
                    ],
                };

                return (
                    <TouchableOpacity onPress={() => handlePress(index)} key={index}>
                        <Animated.Image
                            source={mood.image}
                            style={[
                                styles.image,
                                { left: positions[index].x, top: positions[index].y },
                                animatedStyle,
                            ]}
                            accessibilityLabel={`Mood ${index + 1}`}
                        />
                    </TouchableOpacity>
                );
            })}
        </View>
    );
});

const styles = StyleSheet.create({
    mood_picker_view: {
        width: CONTAINER_SIZE,
        height: CONTAINER_SIZE,
        position: 'relative',
        alignSelf: 'center',
        marginVertical: 20,
    },
    image: {
        width: IMAGE_SIZE,
        height: IMAGE_SIZE,
        position: 'absolute',
    },
});

export default MoodCircle;