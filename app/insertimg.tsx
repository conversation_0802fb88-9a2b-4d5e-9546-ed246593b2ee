import React, { useEffect, useRef } from 'react';

import rnFS from 'react-native-fs';

import {
    Text,
    View,
    StyleSheet,
    TouchableOpacity,
    KeyboardAvoidingView,
    Platform,
    Alert,
} from 'react-native';

import {
    RichText,
    useEditorBridge,
    editorHtml,
    Toolbar,
    DEFAULT_TOOLBAR_ITEMS,
} from '@10play/tentap-editor';


import {
    Camera,
    useCameraDevice,
    useCameraPermission,
} from 'react-native-vision-camera';

import { useSafeAreaInsets } from 'react-native-safe-area-context';


// export const WithVisionCamera = () => {
export default function InsertimgScreen() {
    const insets = useSafeAreaInsets(); // 获取安全区域的边距

    const { hasPermission, requestPermission } = useCameraPermission();
    const [cameraIsOn, setCameraIsOn] = React.useState(false);
    const camera = useRef<Camera>(null);

    useEffect(() => {
        // 修改HTML模板以添加图片样式
        const customHtml = editorHtml.replace('</head>', 
            `<style>
                img {
                    width: 33% !important;
                    height: auto !important;
                    display: block !important;
                    margin-left: auto !important;
                    margin-right: auto !important;
                    border: 10px solid #00000010;
                }
            </style>
            </head>`);
            
        rnFS.writeFile(
            rnFS.CachesDirectoryPath + '/indexr.html',
            customHtml,
            'utf8'
        );
    }, []);
    
    useEffect(() => {
        if (!hasPermission) {
            requestPermission();
        }
    }, [hasPermission]);
    
    const editor = useEditorBridge({
        autofocus: true,
        avoidIosKeyboard: true,
    });

    // 获取并打印编辑器内容的方法
    const printEditorContent = async () => {
        try {
            const content = await editor.getHTML();
            console.log('编辑器内容:', content);
        } catch (error) {
            console.error('获取内容失败:', error);
            Alert.alert('错误', '获取编辑器内容失败');
        }
    };

    const device = useCameraDevice('back');

    const EditorCamera = device ? (
        <>
            <Camera
                ref={camera}
                style={StyleSheet.absoluteFill}
                device={device}
                isActive={true}
                photo={true}
            />
            <View
                style={{
                    width: '100%',
                    justifyContent: 'center',
                    alignItems: 'center',
                    position: 'absolute',
                    bottom: 50,
                    left: 0,
                }}
            >
                <TouchableOpacity
                    style={{
                        height: 80,
                        width: 80,

                        borderRadius: 50,
                        borderWidth: 4,
                        borderColor: '#ffffff44',
                        backgroundColor: '#ffffffbb',
                    }}
                    onPress={async () => {
                        if (!camera.current) {
                            return;
                        }
                        const file = await camera.current.takePhoto();
                        const name = 'test' + new Date().getTime() + '.jpeg';
                        await rnFS.moveFile(
                            file.path ?? '',
                            rnFS.CachesDirectoryPath + '/' + name
                        );
                        setCameraIsOn(false);
                        editor.setImage(`file://${rnFS.CachesDirectoryPath}/${name}`);
                        const editorState = editor.getEditorState();
                        editor.setSelection(
                            editorState.selection.from,
                            editorState.selection.from
                        );
                        editor.focus();
                    }}
                />
            </View>
        </>
    ) : null;

    return (
        <View style={[
            exampleStyles.fullScreen,
            {
                paddingTop: insets.top,
            }
        ]}>
            <RichText
                editor={editor}
                source={{
                    uri: 'file://' + rnFS.CachesDirectoryPath + '/indexr.html',
                }}
                allowFileAccess={true}
                allowFileAccessFromFileURLs={true}
                allowUniversalAccessFromFileURLs={true}
                originWhitelist={['*']}
                mixedContentMode="always"
                allowingReadAccessToURL={'file://' + rnFS.CachesDirectoryPath}
            />
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={exampleStyles.keyboardAvoidingView}
            >
                {/* 添加一个独立的打印按钮 */}
                <TouchableOpacity
                    style={exampleStyles.printButton}
                    onPress={printEditorContent}
                >
                    <Text style={exampleStyles.printButtonText}>打印内容</Text>
                </TouchableOpacity>
                <Toolbar
                    items={[
                        {
                            onPress: () => () => {
                                editor.blur();
                                setCameraIsOn(true);
                            },
                            active: () => false,
                            disabled: () => false,
                            // import cameraPngg from '../assets/images/icon/camera.png';
                            // image: () => cameraPngg,
                            image: () => require('../assets/images/icon/camera.png'),
                        },
                        ...DEFAULT_TOOLBAR_ITEMS,
                    ]}
                    editor={editor}
                />
            </KeyboardAvoidingView>
            {cameraIsOn && EditorCamera}
        </View>
    );
};



const exampleStyles = StyleSheet.create({
    fullScreen: {
        flex: 1,
        backgroundColor: 'white',
        // marginTop: 100,
    },
    keyboardAvoidingView: {
        position: 'absolute',
        width: '100%',
        bottom: 0,
    },
    
    printButton: {
        padding: 10,
        backgroundColor: '#007bff',
        alignItems: 'center',
        borderTopWidth: 1,
        borderTopColor: '#ccc',
    },
    printButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
});
