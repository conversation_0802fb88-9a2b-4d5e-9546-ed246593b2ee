import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    StatusBar,
    FlatList,
    Alert,
    ImageBackground
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSQLiteContext } from 'expo-sqlite';
import { router } from 'expo-router';
import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';
import { Delete02Icon } from '@hugeicons/core-free-icons';



export default function NotesScreen() {
    const insets = useSafeAreaInsets();
    const db = useSQLiteContext();
    const [notes, setNotes] = useState<any[]>([]);

    const handleTopLeftBack = () => {
        router.back();
    };

    // 从数据库加载笔记
    const loadNotes = async () => {
        try {
            const result = await db.getAllAsync('SELECT * FROM notes ORDER BY created_at DESC');
            setNotes(result as any[]);
            console.log('加载的笔记数量:', result.length);
        } catch (error) {
            console.error('加载笔记时出错:', error);
            Alert.alert('错误', '加载笔记失败');
        }
    };

    // 删除笔记
    const deleteNote = async (id: number) => {
        Alert.alert(
            '确认删除',
            '你确定要删除这条笔记吗？',
            [
                {
                    text: '取消',
                    style: 'cancel',
                },
                {
                    text: '删除',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await db.runAsync('DELETE FROM notes WHERE id = ?', [id]);
                            await loadNotes(); // 重新加载数据
                            Alert.alert('成功', '笔记已删除');
                        } catch (error) {
                            console.error('删除笔记时出错:', error);
                            Alert.alert('错误', '删除失败');
                        }
                    },
                },
            ]
        );
    };

    // 组件挂载时加载笔记
    useEffect(() => {
        loadNotes();
    }, []);

    // 渲染每个笔记项
    const renderNote = ({ item }: { item: any }) => {
        // 格式化日期
        const formatDate = (dateString: string) => {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        };

        // 移除HTML标签，获取纯文本预览
        const getTextPreview = (htmlContent: string) => {
            const textContent = htmlContent.replace(/<[^>]*>/g, '');
            return textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent;
        };

        return (
            <View style={styles.noteContainer}>
                <View style={styles.noteContent}>
                    <Text style={styles.noteTitle}>{item.title}</Text>
                    <Text style={styles.noteMood}>心情: {item.mood_name}</Text>
                    <Text style={styles.notePreview}>{getTextPreview(item.content)}</Text>
                    <Text style={styles.noteDate}>{formatDate(item.created_at)}</Text>
                </View>
                <TouchableOpacity 
                    style={styles.deleteButton}
                    onPress={() => deleteNote(item.id)}
                >
                    <HugeiconsIcon
                        icon={Delete02Icon}
                        size={20}
                        color="#ff4444"
                        strokeWidth={1.5}
                    />
                </TouchableOpacity>
            </View>
        );
    };

    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}>我的笔记</Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        {notes.length === 0 ? (
                            <View style={styles.emptyContainer}>
                                <Text style={styles.emptyText}>暂无笔记</Text>
                                <Text style={styles.emptySubText}>去写下你的第一篇心情笔记吧</Text>
                            </View>
                        ) : (
                            <FlatList
                                data={notes}
                                renderItem={renderNote}
                                keyExtractor={(item) => item.id.toString()}
                                showsVerticalScrollIndicator={false}
                                contentContainerStyle={styles.listContainer}
                            />
                        )}
                    </View>
                </View>
            </ImageBackground>
        </>
    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,
        width: '100%',
        height: '100%',
    },
    all: {
        flex: 1,
    },
    head_nav: {
        height: 50,
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },
    content: {
        flex: 1,
        padding: 16,
    },
    noteContainer: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: 12,
        marginBottom: 12,
        padding: 16,
        flexDirection: 'row',
        alignItems: 'flex-start',
    },
    noteContent: {
        flex: 1,
        marginRight: 12,
    },
    noteTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 8,
    },
    noteMood: {
        fontSize: 14,
        color: '#007AFF',
        marginBottom: 8,
        fontWeight: '500',
    },
    notePreview: {
        fontSize: 16,
        color: '#666',
        lineHeight: 24,
        marginBottom: 8,
    },
    noteDate: {
        fontSize: 12,
        color: '#999',
    },
    deleteButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: 'rgba(255, 68, 68, 0.1)',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 10,
    },
    emptySubText: {
        fontSize: 16,
        color: '#666',
    },
    listContainer: {
        paddingBottom: 20,
    },
}); 