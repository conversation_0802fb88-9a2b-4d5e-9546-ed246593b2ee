import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ImageBackground,
    StatusBar,
    ScrollView,
    Alert
} from 'react-native';
import { router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSQLiteContext } from 'expo-sqlite';
import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';



export default function DevSqlNotesScreen() {
    const insets = useSafeAreaInsets();
    const db = useSQLiteContext();
    const [notes, setNotes] = useState<any[]>([]);

    const handleTopLeftBack = () => {
        router.back();
    };

    // 清空notes表数据
    const clearAllNotes = async () => {
        Alert.alert(
            '确认清空',
            '确定要清空所有笔记数据吗？\n此操作不可撤销！',
            [
                {
                    text: '取消',
                    style: 'cancel',
                },
                {
                    text: '确定清空',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await db.runAsync('DELETE FROM notes');
                            await loadNotes(); // 重新加载数据
                            Alert.alert('成功', '所有笔记数据已清空');
                        } catch (error) {
                            console.error('清空数据时出错:', error);
                            Alert.alert('错误', '清空数据失败');
                        }
                    },
                },
            ]
        );
    };

    // 删除单条笔记
    const deleteNote = async (id: number) => {
        Alert.alert(
            '确认删除',
            '确定要删除这条笔记吗？',
            [
                {
                    text: '取消',
                    style: 'cancel',
                },
                {
                    text: '删除',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await db.runAsync('DELETE FROM notes WHERE id = ?', [id]);
                            await loadNotes(); // 重新加载数据
                            Alert.alert('成功', '笔记已删除');
                        } catch (error) {
                            console.error('删除笔记时出错:', error);
                            Alert.alert('错误', '删除笔记失败');
                        }
                    },
                },
            ]
        );
    };

    // 加载notes表数据
    const loadNotes = async () => {
        try {
            const result = await db.getAllAsync('SELECT * FROM notes ORDER BY id ASC');
            setNotes(result as any[]);
        } catch (error) {
            console.error('加载notes表数据时出错:', error);
        }
    };

    useEffect(() => {
        loadNotes();
    }, []);

    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}>Notes 表</Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000"
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        <View style={styles.clearButtonContainer}>
                            <TouchableOpacity 
                                style={styles.clearButton}
                                onPress={clearAllNotes}
                            >
                                <Text style={styles.clearButtonText}>🗑️ 一键清空数据表</Text>
                            </TouchableOpacity>
                        </View>

                        <ScrollView>
                            <Text style={{fontSize: 25, fontWeight: 'bold', color: '#333', marginBottom: 10}}>字段类型</Text>
                            <Text>id: INTEGER PRIMARY KEY AUTOINCREMENT</Text>
                            <Text>mongodb_id: TEXT DEFAULT NULL</Text>
                            <Text>sync_mongodb_time: DATETIME DEFAULT NULL</Text>

                            <Text>title: TEXT</Text>
                            <Text>content: TEXT</Text>

                            <Text>mood_name: TEXT DEFAULT 'happy'</Text>
                            <Text>mood_score: INTEGER DEFAULT 10</Text>

                            <Text>mood_app_in_img_name: TEXT DEFAULT 'app_in_mood1'</Text>
                            <Text>mood_user_create_url: TEXT DEFAULT NULL</Text>

                            <Text>yyyymm: INTEGER DEFAULT 199911</Text>
                            <Text>created_at: DATETIME DEFAULT CURRENT_TIMESTAMP</Text>
                            <Text>updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP</Text>

                            <Text style={{fontSize: 25, fontWeight: 'bold', color: '#333', marginTop: 100, marginBottom: 10}}>表数据</Text>

                            {notes.length === 0 ? (
                                <Text>notes 表暂无数据</Text>
                            ) : (
                                notes.map((item) => (
                                    <View key={item.id} style={styles.noteItem}>
                                        <View style={styles.noteContent}>
                                            <Text>id: {item.id}</Text>
                                            <Text style={item.mongodb_id ? {color: 'green'} : {color: 'red'}}>mongodb_id: {item.mongodb_id}</Text>
                                            <Text style={item.mongodb_id ? {color: 'green'} : {color: 'red'}}>sync_mongodb_time: {item.sync_mongodb_time}</Text>

                                            <Text>title: {item.title}</Text>
                                            <Text>content: {item.content}</Text>

                                            <Text>mood_name: {item.mood_name}</Text>
                                            <Text>mood_score: {item.mood_score}</Text>

                                            <Text>mood_app_in_img_name: {item.mood_app_in_img_name}</Text>
                                            <Text>mood_user_create_url: {item.mood_user_create_url}</Text>

                                            <Text>yyyymm: {item.yyyymm}</Text>
                                            <Text>created_at: {item.created_at}</Text>
                                            <Text>updated_at: {item.updated_at}</Text>
                                        </View>
                                        
                                        <TouchableOpacity 
                                            style={styles.deleteButton}
                                            onPress={() => deleteNote(item.id)}
                                        >
                                            <Text style={styles.deleteButtonText}>删除</Text>
                                        </TouchableOpacity>
                                    </View>
                                ))
                            )}
                        </ScrollView>
                    </View>
                </View>
            </ImageBackground>
        </>
    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,
        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,
    },

    head_nav: {
        height: 50,
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: 'red',
    },
    
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },

    content: {
        flex: 1,
        padding: 16,
    },

    clearButtonContainer: {
        alignItems: 'center',
        marginBottom: 16,
    },

    clearButton: {
        backgroundColor: '#f40', // 红色按钮
        paddingVertical: 10,
        paddingHorizontal: 20,
        // borderRadius: 8,
    },

    clearButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },

    noteItem: {
        marginBottom: 50,
        backgroundColor: '#f9f9f9',
        padding: 12,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#ddd',
    },

    noteContent: {
        marginBottom: 12,
    },

    deleteButton: {
        backgroundColor: '#ff4444',
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 4,
        alignSelf: 'flex-start',
    },

    deleteButtonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: 'bold',
    },
});
