import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform,
    TextInput,
    Alert
} from 'react-native';

import { router } from 'expo-router';

import { useSQLiteContext } from 'expo-sqlite';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';
import { ArrowLeft02Icon } from '@hugeicons/core-free-icons';



export default function SqladdScreen() {
    const insets = useSafeAreaInsets(); // 获取安全区域的边距
    
    const [firstInput, setFirstInput] = useState('');
    const [secondInput, setSecondInput] = useState('');


    const db = useSQLiteContext();

    const handleTopLeftBack = () => {
        router.back();
    };

    // 处理保存操作
    const handleSave = async () => {
        // 检查输入是否为空
        if (firstInput === '' || secondInput === '') {
            Alert.alert('提示', '请填写完整信息后再保存');
            return;
        }

        try {
            // 将数据插入到数据库
            await db.runAsync(
                'INSERT INTO items (name, text) VALUES (?, ?)',
                [firstInput, secondInput]
            );
            
            console.log('数据保存成功:', firstInput, secondInput);
            
            // 显示成功提示
            Alert.alert('成功', '数据已保存到数据库', [
                {
                    text: '确定',
                    onPress: () => {
                        // 清空输入框
                        setFirstInput('');
                        setSecondInput('');
                    }
                }
            ]);
            
        } catch (error) {
            console.error('保存数据时出错:', error);
            Alert.alert('错误', '保存数据失败，请重试');
        }
    };

    

    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}> sql add </Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        <Text style={styles.label}>名称:</Text>
                        <TextInput
                            style={styles.textInput}
                            value={firstInput}
                            onChangeText={setFirstInput}
                            placeholder="请输入名称"
                            placeholderTextColor="#999"
                        />
                        
                        <Text style={styles.label}>内容:</Text>
                        <TextInput
                            style={styles.textInput}
                            value={secondInput}
                            onChangeText={setSecondInput}
                            placeholder="请输入内容"
                            placeholderTextColor="#999"
                            multiline={true}
                            numberOfLines={4}
                            textAlignVertical="top"
                        />
                        
                        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                            <Text style={styles.saveButtonText}>保存</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </ImageBackground>
        </>

    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,

        // justifyContent: 'center',
        // alignItems: 'center',
        // backgroundColor: '#f40',
    },

    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },


    content: {
        flex: 1,
        padding: 16,
        // backgroundColor: 'white',
    },

    label: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 8,
        marginTop: 20,
    },

    textInput: {
        minHeight: 50,
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 12,
        fontSize: 16,
        backgroundColor: 'white',
        color: '#333',
    },

    saveButton: {
        backgroundColor: '#007AFF',
        borderRadius: 8,
        paddingVertical: 15,
        paddingHorizontal: 24,
        marginTop: 30,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },

    saveButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
});
