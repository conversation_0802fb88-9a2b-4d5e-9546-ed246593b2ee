import React, { useState, useEffect } from 'react';

import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform,
    ScrollView
} from 'react-native';

import * as Font from 'expo-font';

import { router } from 'expo-router';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';



export default function zdevfontfamilyScreen() {
    const insets = useSafeAreaInsets(); // 获取安全区域的边距
    const [fontsLoaded, setFontsLoaded] = useState(false);

    useEffect(() => {
        loadFonts();
    }, []);

    const loadFonts = async () => {
        try {
            await Font.loadAsync({
                'Brushzing-Regular': require('../assets/fonts/Brushzing-Regular.otf'),
                '2Peas4ThOfJuly-ARj6': require('../assets/fonts/2Peas4ThOfJuly-ARj6.ttf'),
                'Cecil1-nZDg': require('../assets/fonts/Cecil1-nZDg.ttf'),
                'CuteAurora-PK3lZ': require('../assets/fonts/CuteAurora-PK3lZ.ttf'),
                'DatangStory-drxBK': require('../assets/fonts/DatangStory-drxBK.otf'),
                'Biscuitos': require('../assets/fonts/Biscuitos.otf'),
                'BlueBubbles-gwE7P': require('../assets/fonts/BlueBubbles-gwE7P.otf'),
                'Diet Again': require('../assets/fonts/Diet Again.otf'),
                'FarahJawhar-Regular': require('../assets/fonts/FarahJawhar-Regular.otf'),
                'FearFluid-Zpp13': require('../assets/fonts/FearFluid-Zpp13.ttf'),
                'Idealy-V4J6B': require('../assets/fonts/Idealy-V4J6B.otf'),
                'InFormal_Style_Regular': require('../assets/fonts/InFormal_Style_Regular.otf'),
                'Kenangan-Regular': require('../assets/fonts/Kenangan-Regular.otf'),
                'MessyHandwritten-Regular': require('../assets/fonts/MessyHandwritten-Regular.ttf'),
                'MotleyForcesRegular-w1rZ3': require('../assets/fonts/MotleyForcesRegular-w1rZ3.ttf'),
                
                'PajamaPants': require('../assets/fonts/PajamaPants.ttf'),
                'PermanentMarker-Regular': require('../assets/fonts/PermanentMarker-Regular.ttf'),
                'Sorcerous_Mouse': require('../assets/fonts/Sorcerous_Mouse.otf'),
                'SpaceMono-Regular': require('../assets/fonts/SpaceMono-Regular.ttf'),
                'Sunny Spells': require('../assets/fonts/Sunny Spells.ttf'),
                'Whitenice': require('../assets/fonts/Whitenice.otf'),
                'desard': require('../assets/fonts/desard.otf'),
                'hipch___': require('../assets/fonts/hipch___.ttf'),
            });
            setFontsLoaded(true);
        } catch (error) {
            console.error('Error loading fonts:', error);
            setFontsLoaded(true); // 即使失败也设置为true，显示默认字体
        }
    };

    const handleTopLeftBack = () => {
        router.back();
    };

    // 字体数据 - 使用文件名（不含扩展名）作为fontFamily
    const fonts = [
        { name: 'Brushzing-Regular', displayName: 'Brushzing Regular', sample: 'January February March' },
        { name: '2Peas4ThOfJuly-ARj6', displayName: '2Peas 4th of July', sample: 'April May June July' },
        { name: 'Cecil1-nZDg', displayName: 'Cecil1', sample: 'August September October' },
        { name: 'CuteAurora-PK3lZ', displayName: 'Cute Aurora', sample: 'November December 2024' },
        { name: 'DatangStory-drxBK', displayName: 'Datang Story', sample: 'Monthly Calendar Display' },
        { name: 'Biscuitos', displayName: 'Biscuitos', sample: 'Sweet Calendar Style' },
        { name: 'BlueBubbles-gwE7P', displayName: 'Blue Bubbles', sample: 'Bubble Font Display' },
        { name: 'Diet Again', displayName: 'Diet Again', sample: 'Fresh New Year' },
        { name: 'FarahJawhar-Regular', displayName: 'Farah Jawhar', sample: 'Arabic Style Calendar' },
        { name: 'FearFluid-Zpp13', displayName: 'Fear Fluid', sample: 'Spooky Halloween Month' },
        { name: 'Idealy-V4J6B', displayName: 'Idealy', sample: 'Perfect Design Font' },
        { name: 'InFormal_Style_Regular', displayName: 'Informal Style', sample: 'Casual Calendar View' },
        { name: 'Kenangan-Regular', displayName: 'Kenangan', sample: 'Memory Lane Calendar' },
        { name: 'MessyHandwritten-Regular', displayName: 'Messy Handwritten', sample: 'Hand Drawn Dates' },
        { name: 'MotleyForcesRegular-w1rZ3', displayName: 'Motley Forces', sample: 'Dynamic Calendar' },
    
        { name: 'PajamaPants', displayName: 'Pajama Pants', sample: 'Cozy Weekend View' },
        { name: 'PermanentMarker-Regular', displayName: 'Permanent Marker', sample: 'Bold Calendar Marks' },
        { name: 'Sorcerous_Mouse', displayName: 'Sorcerous Mouse', sample: 'Magic Calendar Spells' },
        { name: 'SpaceMono-Regular', displayName: 'Space Mono', sample: 'Tech Calendar Display' },
        { name: 'Sunny Spells', displayName: 'Sunny Spells', sample: 'Bright Summer Days' },
        { name: 'Whitenice', displayName: 'White Nice', sample: 'Clean White Calendar' },
        { name: 'desard', displayName: 'Desard', sample: 'Desert Sand Calendar' },
        { name: 'hipch___', displayName: 'Hipch', sample: 'Hip Hop Calendar Style' }
    ];

    const monthNames = [
        'January',
        'February', 
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
    ];



    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}>Font Test</Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          
                        
                        {!fontsLoaded && (
                            <Text style={styles.loadingText}>Loading fonts...</Text>
                        )}
                        
                        {fonts.map((font, index) => (
                            <View key={index} style={styles.fontCard}>
                                <Text style={styles.fontName}>{font.displayName}</Text>
                                <Text style={styles.fontFileName}>文件: {font.name}</Text>
                                
                   
                                
                                {/* 月份名称展示 */}
                                <View style={styles.multiTextContainer}>
                                    {monthNames.map((month, monthIndex) => (
                                        <Text key={monthIndex} style={[styles.multiText, { fontFamily: font.name }]}>
                                            • {month}
                                        </Text>
                                    ))}
                                </View>
                            </View>
                        ))}
                        
                        {/* 对比展示 */}
                        <View style={styles.comparisonSection}>
                            <Text style={styles.sectionTitle}>Font Comparison</Text>
                            <Text style={styles.comparisonText}>Same text, different font effects:</Text>
                            
                            {fonts.map((font, index) => (
                                <View key={index} style={styles.comparisonItem}>
                                    <Text style={styles.comparisonLabel}>{font.displayName}:</Text>
                                    <Text style={[styles.comparisonSample, { fontFamily: font.name }]}>
                                        January to December Calendar
                                    </Text>
                                </View>
                            ))}
                        </View>
                    </ScrollView>
                </View>
            </ImageBackground>
        </>

    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,

        // justifyContent: 'center',
        // alignItems: 'center',
        // backgroundColor: '#f40',
    },

    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 28,
        fontWeight: 'bold',
        color: '#333',
    },


    content: {
        flex: 1,
        padding: 16,
        // backgroundColor: 'white',
    },

    // 页面标题样式
    pageTitle: {
        fontSize: 36,
        fontWeight: 'bold',
        color: '#333',
        textAlign: 'center',
        marginBottom: 8,
        marginTop: 10,
    },

    subtitle: {
        fontSize: 24,
        color: '#666',
        textAlign: 'center',
        marginBottom: 20,
    },

    loadingText: {
        fontSize: 24,
        color: '#999',
        textAlign: 'center',
        fontStyle: 'italic',
        marginVertical: 20,
    },

    // 字体卡片样式
    fontCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        marginBottom: 20,
        padding: 16,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },

    fontName: {
        fontSize: 26,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 4,
    },

    fontFileName: {
        fontSize: 16,
        color: '#999',
        marginBottom: 12,
        fontStyle: 'italic',
    },

    // 示例文本样式
    sampleLarge: {
        fontSize: 32,
        color: '#333',
        marginBottom: 8,
    },

    sampleMedium: {
        fontSize: 26,
        color: '#444',
        marginBottom: 8,
    
    },

    sampleSmall: {
        fontSize: 22,
        color: '#555',
        marginBottom: 12,
    },

    // 多行文本容器
    multiTextContainer: {
        backgroundColor: 'rgba(0, 0, 0, 0.05)',
    
        marginTop: 8,
    },

    multiText: {
        fontSize: 38,
        lineHeight: 50,
        marginBottom: 4,
    },

    // 对比展示样式
    comparisonSection: {
        marginTop: 20,
        padding: 16,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderRadius: 12,
        marginBottom: 30,
    },

    sectionTitle: {
        fontSize: 28,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 8,
        textAlign: 'center',
    },

    comparisonText: {
        fontSize: 22,
        color: '#666',
        textAlign: 'center',
        marginBottom: 16,
    },

    comparisonItem: {
        marginBottom: 12,
        paddingBottom: 8,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },

    comparisonLabel: {
        fontSize: 16,
        color: '#999',
        marginBottom: 4,
    },

    comparisonSample: {
        fontSize: 24,
        lineHeight: 32,
        color: '#333',
    },
});
