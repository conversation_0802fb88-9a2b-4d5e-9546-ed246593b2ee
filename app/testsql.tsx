import React, { useState, useEffect } from 'react';
import { View, Text, Button, TextInput, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import * as SQLite from 'expo-sqlite';

// 定义数据类型
interface Item {
    id: number;
    text: string;
}

export default function TestsqlScreen() {
    const [items, setItems] = useState<Item[]>([]);
    const [text, setText] = useState('');
    const [forceUpdate, setForceUpdate] = useState(0);
    const [db, setDb] = useState<SQLite.SQLiteDatabase | null>(null);

    // 初始化数据库
    useEffect(() => {
        const initDatabase = async () => {
            try {
                // 打开数据库
                const database = await SQLite.openDatabaseAsync('databaseName');
                setDb(database);

                // 创建表
                await database.execAsync(`
                    PRAGMA journal_mode = WAL;
                    CREATE TABLE IF NOT EXISTS items (
                        id INTEGER PRIMARY KEY NOT NULL, 
                        text TEXT NOT NULL
                    );
                `);

                console.log('数据库初始化成功');
            } catch (error) {
                console.error('数据库初始化错误:', error);
            }
        };

        initDatabase();

        // 清理函数
        return () => {
            if (db) {
                db.closeAsync();
            }
        };
    }, []);

    // 强制更新后获取数据
    useEffect(() => {
        if (db) {
            fetchItems();
        }
    }, [forceUpdate, db]);

    // 获取所有项目
    const fetchItems = async () => {
        if (!db) return;

        try {
            const result = await db.getAllAsync<Item>('SELECT * FROM items');
            setItems(result);
        } catch (error) {
            console.error('获取项目错误:', error);
        }
    };

    // 添加新项目
    const addItem = async () => {
        if (!db || text.trim() === '') return;

        try {
            await db.runAsync('INSERT INTO items (text) VALUES (?)', text);
            console.log('项目添加成功');
            setText('');
            setForceUpdate(prev => prev + 1);
        } catch (error) {
            console.error('添加项目错误:', error);
        }
    };

    // 删除项目
    const deleteItem = async (id: number) => {
        if (!db) return;

        try {
            await db.runAsync('DELETE FROM items WHERE id = ?', id);
            console.log('项目删除成功');
            setForceUpdate(prev => prev + 1);
        } catch (error) {
            console.error('删除项目错误:', error);
        }
    };

    return (
        <View style={styles.container}>
            <Text style={styles.title}>SQLite 示例</Text>

            <View style={styles.inputContainer}>
                <TextInput
                    style={styles.input}
                    placeholder="输入新项目"
                    value={text}
                    onChangeText={setText}
                />


                <TouchableOpacity style={styles.small_button} onPress={() => addItem()}>
                    <Text style={styles.small_button_text}>添加</Text>
                </TouchableOpacity>

            </View>

            <Text style={styles.subtitle}>项目列表:</Text>
            {items.length === 0 ? (
                <Text style={styles.emptyText}>没有项目</Text>
            ) : (
                <FlatList
                    data={items}
                    keyExtractor={item => item.id.toString()}
                    renderItem={({ item }) => (
                        <View style={styles.itemContainer}>
                            <Text>{item.text}</Text>


                            <TouchableOpacity style={styles.small_button} onPress={() => deleteItem(item.id)}>
                                <Text style={styles.small_button_text}>删除</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        marginTop: 40,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 20,
    },
    subtitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginTop: 20,
        marginBottom: 10,
    },

    inputContainer: {
        flexDirection: 'row',
        marginBottom: 20,
    },

    input: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',

        padding: 10,
        marginRight: 10,
    },

    itemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',

        padding: 5,
        marginBottom: 10,

        borderWidth: 1,
        borderColor: '#ccc',
    },
    emptyText: {
        fontSize: 16,
        color: '#999',
        textAlign: 'center',
        marginTop: 20,
    },


    small_button: {
        backgroundColor: '#2196F3',
        padding: 10,
    },
    small_button_text: {
        color: '#fff',
        textAlign: 'center',
    },

});