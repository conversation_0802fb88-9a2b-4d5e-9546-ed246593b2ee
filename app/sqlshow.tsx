import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform,
    FlatList,
    Alert
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useSQLiteContext } from 'expo-sqlite';

import { router } from 'expo-router';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';
import { Delete02Icon } from '@hugeicons/core-free-icons';

// 定义数据项的类型
interface Item {
    id: number;
    name: string;
    text: string;
}

export default function SqlshowScreen() {
    const insets = useSafeAreaInsets();
    
    const db = useSQLiteContext();
    
    const [items, setItems] = useState<Item[]>([]);

    const handleTopLeftBack = () => {
        router.back();
    };

    // 从数据库加载数据
    const loadData = async () => {
        try {
            const result = await db.getAllAsync('SELECT * FROM items ORDER BY id DESC');
            setItems(result as Item[]);
        } catch (error) {
            console.error('加载数据时出错:', error);
            Alert.alert('错误', '加载数据失败');
        }
    };

    // 删除数据项
    const deleteItem = async (id: number) => {
        Alert.alert(
            '确认删除',
            '你确定要删除这条记录吗？',
            [
                {
                    text: '取消',
                    style: 'cancel',
                },
                {
                    text: '删除',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await db.runAsync('DELETE FROM items WHERE id = ?', [id]);
                            await loadData(); // 重新加载数据
                            Alert.alert('成功', '记录已删除');
                        } catch (error) {
                            console.error('删除数据时出错:', error);
                            Alert.alert('错误', '删除失败');
                        }
                    },
                },
            ]
        );
    };

    // 组件挂载时加载数据
    useEffect(() => {
        loadData();
    }, []);



    // 渲染每个数据项
    const renderItem = ({ item }: { item: Item }) => (
        <View style={styles.itemContainer}>
            <View style={styles.itemContent}>
                <Text style={styles.itemName}>{item.name}</Text>
                <Text style={styles.itemText}>{item.text}</Text>
            </View>
            <TouchableOpacity 
                style={styles.deleteButton}
                onPress={() => deleteItem(item.id)}
            >
                <HugeiconsIcon
                    icon={Delete02Icon}
                    size={20}
                    color="#ff4444"
                    strokeWidth={1.5}
                />
            </TouchableOpacity>
        </View>
    );

    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}> sql show </Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        {items.length === 0 ? (
                            <View style={styles.emptyContainer}>
                                <Text style={styles.emptyText}>暂无数据</Text>
                                <Text style={styles.emptySubText}>去添加页面添加一些数据吧</Text>
                            </View>
                        ) : (
                            <FlatList
                                data={items}
                                renderItem={renderItem}
                                keyExtractor={(item) => item.id.toString()}
                                showsVerticalScrollIndicator={false}
                                contentContainerStyle={styles.listContainer}
                            />
                        )}
                    </View>
                </View>
            </ImageBackground>
        </>
    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,

        // justifyContent: 'center',
        // alignItems: 'center',
        // backgroundColor: '#f40',
    },

    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },


    content: {
        flex: 1,
        padding: 16,
    },

    itemContainer: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: 12,
        marginBottom: 12,
        padding: 16,
        flexDirection: 'row',
        alignItems: 'flex-start',
    },

    itemContent: {
        flex: 1,
        marginRight: 12,
    },

    itemName: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 8,
    },

    itemText: {
        fontSize: 16,
        color: '#666',
        lineHeight: 24,
    },

    deleteButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: 'rgba(255, 68, 68, 0.1)',
    },



    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },

    emptyText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 10,
    },

    emptySubText: {
        fontSize: 16,
        color: '#666',
    },

    listContainer: {
        paddingBottom: 20,
    },
});
