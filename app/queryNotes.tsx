import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    StatusBar,
    ScrollView,
    Alert,
    ImageBackground,
    RefreshControl,
    Image
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSQLiteContext } from 'expo-sqlite';
import { router } from 'expo-router';
import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';
import { RefreshIcon } from '@hugeicons/core-free-icons';
import { Delete01Icon } from '@hugeicons/core-free-icons';

export default function QueryNotesScreen() {
    const insets = useSafeAreaInsets();
    const db = useSQLiteContext();
    const [notes, setNotes] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    const handleTopLeftBack = () => {
        router.back();
    };

    // 从数据库加载所有笔记数据
    const loadNotesData = async () => {
        setIsLoading(true);
        try {
            const result = await db.getAllAsync('SELECT * FROM notes ORDER BY created_at DESC');
            setNotes(result as any[]);
            console.log('查询到的notes表数据:', result);
            console.log('数据条数:', result.length);
            
            // 打印每条记录的详细信息
            result.forEach((note, index) => {
                console.log(`记录 ${index + 1}:`, note);
            });
            
        } catch (error) {
            console.error('查询notes表时出错:', error);
            Alert.alert('错误', '查询数据失败');
        } finally {
            setIsLoading(false);
        }
    };

    // 删除笔记功能
    const deleteNote = async (noteId: number, noteTitle: string) => {
        Alert.alert(
            '确认删除',
            `确定要删除笔记"${noteTitle}"吗？\n此操作不可撤销。`,
            [
                {
                    text: '取消',
                    style: 'cancel',
                },
                {
                    text: '删除',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            // 执行删除操作
                            await db.runAsync('DELETE FROM notes WHERE id = ?', [noteId]);
                            
                            // 删除成功后重新加载数据
                            await loadNotesData();
                            
                            Alert.alert('成功', '笔记已删除');
                        } catch (error) {
                            console.error('删除笔记时出错:', error);
                            Alert.alert('错误', '删除笔记失败');
                        }
                    },
                },
            ]
        );
    };

    // 获取表结构信息
    const getTableInfo = async () => {
        try {
            const tableInfo = await db.getAllAsync('PRAGMA table_info(notes)');
            console.log('notes表结构:', tableInfo);
            
            Alert.alert(
                'notes表结构',
                tableInfo.map((col: any) => `${col.name}: ${col.type}`).join('\n')
            );
        } catch (error) {
            console.error('获取表结构时出错:', error);
            Alert.alert('错误', '获取表结构失败');
        }
    };

    // 组件挂载时加载数据
    useEffect(() => {
        loadNotesData();
    }, []);

    // 格式化日期显示
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    };

    // 移除HTML标签，获取纯文本
    const stripHtml = (html: string) => {
        return html.replace(/<[^>]*>/g, '');
    };

    // 提取HTML内容中的图片
    const extractImages = (htmlContent: string) => {
        const imgRegex = /<img[^>]+src="([^"]+)"/g;
        const images: string[] = [];
        let match;
        
        while ((match = imgRegex.exec(htmlContent)) !== null) {
            images.push(match[1]);
        }
        
        return images;
    };

    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}>Notes表数据查询</Text>

                        <TouchableOpacity onPress={getTableInfo}>
                            <Text style={styles.tableInfoButton}>表结构</Text>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.content}>
                        <View style={styles.statsContainer}>
                            <Text style={styles.statsText}>
                                总记录数: {notes.length}
                            </Text>
                            <TouchableOpacity 
                                style={styles.refreshButton}
                                onPress={loadNotesData}
                                disabled={isLoading}
                            >
                                <HugeiconsIcon
                                    icon={RefreshIcon}
                                    size={20}
                                    color="#007AFF"
                                    strokeWidth={1.5}
                                />
                                <Text style={styles.refreshText}>
                                    {isLoading ? '加载中...' : '刷新'}
                                </Text>
                            </TouchableOpacity>
                        </View>

                        <ScrollView 
                            style={styles.dataContainer}
                            refreshControl={
                                <RefreshControl
                                    refreshing={isLoading}
                                    onRefresh={loadNotesData}
                                />
                            }
                        >
                            {notes.length === 0 ? (
                                <View style={styles.emptyContainer}>
                                    <Text style={styles.emptyText}>
                                        {isLoading ? '正在加载数据...' : 'notes表中暂无数据'}
                                    </Text>
                                </View>
                            ) : (
                                notes.map((note, index) => {
                                    const images = extractImages(note.content);
                                    
                                    return (
                                        <View key={note.id} style={styles.recordContainer}>
                                            <View style={styles.recordHeader}>
                                                <Text style={styles.recordTitle}>
                                                    记录 #{index + 1} (ID: {note.id})
                                                </Text>
                                                <TouchableOpacity
                                                    style={styles.deleteButton}
                                                    onPress={() => deleteNote(note.id, note.title)}
                                                >
                                                    <HugeiconsIcon
                                                        icon={Delete01Icon}
                                                        size={20}
                                                        color="#FF3B30"
                                                        strokeWidth={1.5}
                                                    />
                                                    <Text style={styles.deleteButtonText}>删除</Text>
                                                </TouchableOpacity>
                                            </View>
                                            
                                            <View style={styles.fieldContainer}>
                                                <Text style={styles.fieldLabel}>标题:</Text>
                                                <Text style={styles.fieldValue}>{note.title}</Text>
                                            </View>

                                            <View style={styles.fieldContainer}>
                                                <Text style={styles.fieldLabel}>内容:</Text>
                                                <Text style={styles.fieldValue}>
                                                    {stripHtml(note.content).substring(0, 200)}
                                                    {stripHtml(note.content).length > 200 ? '...' : ''}
                                                </Text>
                                            </View>

                                            {/* 显示图片 */}
                                            {images.length > 0 && (
                                                <View style={styles.fieldContainer}>
                                                    <Text style={styles.fieldLabel}>图片 ({images.length}张):</Text>
                                                    <ScrollView 
                                                        horizontal 
                                                        showsHorizontalScrollIndicator={false}
                                                        style={styles.imagesContainer}
                                                    >
                                                        {images.map((imageUri, imgIndex) => (
                                                            <View key={imgIndex} style={styles.imageWrapper}>
                                                                <Image 
                                                                    source={{ uri: imageUri }}
                                                                    style={styles.image}
                                                                    resizeMode="cover"
                                                                />
                                                                <Text style={styles.imageIndex}>
                                                                    图片 {imgIndex + 1}
                                                                </Text>
                                                            </View>
                                                        ))}
                                                    </ScrollView>
                                                </View>
                                            )}

                                            <View style={styles.fieldContainer}>
                                                <Text style={styles.fieldLabel}>心情图片:</Text>
                                                <Text style={styles.fieldValue}>{note.mood_app_in_img_name}</Text>
                                            </View>

                                            <View style={styles.fieldContainer}>
                                                <Text style={styles.fieldLabel}>心情名称:</Text>
                                                <Text style={styles.fieldValue}>{note.mood_name}</Text>
                                            </View>

                                            <View style={styles.fieldContainer}>
                                                <Text style={styles.fieldLabel}>创建时间:</Text>
                                                <Text style={styles.fieldValue}>
                                                    {formatDate(note.created_at)}
                                                </Text>
                                            </View>

                                            <View style={styles.fieldContainer}>
                                                <Text style={styles.fieldLabel}>更新时间:</Text>
                                                <Text style={styles.fieldValue}>
                                                    {formatDate(note.updated_at)}
                                                </Text>
                                            </View>
                                        </View>
                                    );
                                })
                            )}
                        </ScrollView>
                    </View>
                </View>
            </ImageBackground>
        </>
    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,
        width: '100%',
        height: '100%',
    },
    all: {
        flex: 1,
    },
    head_nav: {
        height: 50,
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 10,
    },
    head_nav_title: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
    },
    tableInfoButton: {
        fontSize: 16,
        color: '#007AFF',
        fontWeight: '500',
    },
    content: {
        flex: 1,
        padding: 16,
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: 8,
        padding: 12,
        marginBottom: 16,
    },
    statsText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },
    refreshButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 122, 255, 0.1)',
        borderRadius: 6,
        padding: 8,
    },
    refreshText: {
        fontSize: 14,
        color: '#007AFF',
        marginLeft: 4,
        fontWeight: '500',
    },
    dataContainer: {
        flex: 1,
    },
    recordContainer: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        borderLeftWidth: 4,
        borderLeftColor: '#007AFF',
    },
    recordHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    recordTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(0, 122, 255, 0.1)',
        padding: 8,
        borderRadius: 6,
        flex: 1,
        textAlign: 'center',
        marginRight: 8,
    },
    deleteButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 59, 48, 0.1)',
        borderRadius: 6,
        padding: 8,
        borderWidth: 1,
        borderColor: 'rgba(255, 59, 48, 0.3)',
    },
    deleteButtonText: {
        fontSize: 12,
        color: '#FF3B30',
        marginLeft: 4,
        fontWeight: '600',
    },
    fieldContainer: {
        marginBottom: 8,
    },
    fieldLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#555',
        marginBottom: 2,
    },
    fieldValue: {
        fontSize: 14,
        color: '#333',
        backgroundColor: 'rgba(240, 240, 240, 0.8)',
        padding: 8,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    // 新增图片相关样式
    imagesContainer: {
        marginTop: 4,
    },
    imageWrapper: {
        marginRight: 12,
        alignItems: 'center',
    },
    image: {
        width: 120,
        height: 120,
        borderRadius: 8,
        borderWidth: 2,
        borderColor: '#007AFF',
    },
    imageIndex: {
        fontSize: 12,
        color: '#666',
        marginTop: 4,
        textAlign: 'center',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 16,
        color: '#666',
        textAlign: 'center',
    },
}); 