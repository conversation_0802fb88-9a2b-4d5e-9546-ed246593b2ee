import React, { useState, useEffect, useRef } from 'react';
import {
	View,
	Text,
	StyleSheet,
	Image,
	TouchableOpacity,
	ImageBackground,
	Animated,
	StatusBar,
	Platform,
	FlatList,
	ScrollView
} from 'react-native';

import { Link, useFocusEffect, router } from 'expo-router';

import MoodCircle from '../components/MoodCircle';
import { setGlobalSelectedDate, getMoodImage } from '../appGlobalData';

import {
	SafeAreaProvider,
	useSafeAreaInsets,
} from 'react-native-safe-area-context';

import { useSQLiteContext } from 'expo-sqlite';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { PlusSignCircleIcon } from '@hugeicons/core-free-icons';
import { CircleIcon } from '@hugeicons/core-free-icons';
import { Settings03Icon } from '@hugeicons/core-free-icons';
import { GlobalIcon } from '@hugeicons/core-free-icons';

import env from '../env';



// 每条日记记录
export type DiaryEntry = {
	id: string;         // 唯一 ID，UUID 最好
	timestamp: string;  // ISO 时间戳，比如 "2025-06-04T08:00:00Z"
	content: string;
	mood_img_name: string;
};

// 所有日记，按日期分组（推荐使用 ISO 日期作为 key）
export type DiaryData = {
	[date: string]: DiaryEntry[]; // key 是 "2025-06-04"
};



export default function Index() {
	const insets = useSafeAreaInsets();
	const db = useSQLiteContext();

	const [year, setYear] = useState(0)
	const [month, setMonth] = useState(0)


	const monthNames = [
		'January', 'February', 'March', 'April', 'May', 'June',
		'July', 'August', 'September', 'October', 'November', 'December',
	];

	// 添加状态来存储从数据库读取的真实数据
	const [diaryData, setDiaryData] = useState<DiaryData>({});
	const [isLoading, setIsLoading] = useState(true);

	// 添加轮播状态：存储每个日期当前显示的记录索引
	const [carouselIndices, setCarouselIndices] = useState<{ [key: string]: number }>({});

	// 添加动画值存储，为每个有多个记录的日期创建动画值
	const animatedValues = useRef<{ [key: string]: Animated.Value }>({});

	let [showMoodPicker, setShowMoodPicker] = useState(false)
	const rotateAnim = useRef(new Animated.Value(0)).current;

	// 添加选中日期的状态
	const [selectedDate, setSelectedDate] = useState<string | undefined>(undefined);

	useEffect(() => {
		console.log('--- index useEffect ---', env.BASE_URL, __DEV__);

		// 设置当前年月
		const now = new Date();
		setYear(now.getFullYear());
		setMonth(now.getMonth() + 1);

		getNotesData();
	}, []);

	// 使用 useFocusEffect 来在页面重新获得焦点时刷新数据
	useFocusEffect(
		React.useCallback(() => {
			console.log('--- index useFocusEffect ---', env.BASE_URL, __DEV__);

			getNotesData();

			setShowMoodPicker(false);

			// 重置旋转动画到初始状态
			rotateAnim.setValue(0);
		}, [])
	);

	// 为有多个记录的日期创建动画值
	useEffect(() => {
		Object.entries(diaryData).forEach(([dateKey, entries]) => {
			if (entries.length > 1 && !animatedValues.current[dateKey]) {
				animatedValues.current[dateKey] = new Animated.Value(0);
			}
		});
	}, [diaryData]);

	// 轮播自动切换的useEffect，添加动画效果
	useEffect(() => {
		const interval = setInterval(() => {
			setCarouselIndices(prevIndices => {
				const newIndices = { ...prevIndices };

				// 遍历所有有多条记录的日期
				Object.entries(diaryData).forEach(([dateKey, entries]) => {
					if (entries.length > 1) {
						const currentIndex = newIndices[dateKey] || 0;
						const nextIndex = (currentIndex + 1) % entries.length;

						// 添加滑动动画
						if (animatedValues.current[dateKey]) {
							// 先滑动到右边（隐藏当前图片）
							Animated.sequence([
								Animated.timing(animatedValues.current[dateKey], {
									toValue: 1, // 向右滑动
									duration: 250,
									useNativeDriver: true,
								}),
								// 瞬间重置到左边
								Animated.timing(animatedValues.current[dateKey], {
									toValue: -1, // 重置到左边（不可见位置）
									duration: 0,
									useNativeDriver: true,
								}),
								// 滑动进入中心位置
								Animated.timing(animatedValues.current[dateKey], {
									toValue: 0, // 滑动到中心位置
									duration: 250,
									useNativeDriver: true,
								}),
							]).start();
						}

						// 延迟更新索引，让动画有时间完成第一阶段
						setTimeout(() => {
							setCarouselIndices(prev => ({
								...prev,
								[dateKey]: nextIndex
							}));
						}, 250);
					}
				});

				return newIndices;
			});
		}, 3000); // 每 3 秒切换一次

		return () => clearInterval(interval);
	}, [diaryData]);

	// 获取当前应该显示的记录
	const getCurrentEntry = (dateKey: string, entries: DiaryEntry[]) => {
		if (entries.length <= 1) return entries[0];
		const currentIndex = carouselIndices[dateKey] || 0;
		return entries[currentIndex];
	};

	// 查询 notes 表本月数据
	const getNotesData = async () => {
		try {
			setIsLoading(true);

			const now = new Date();
			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			const currentYYYYMM = parseInt(`${year}${month}`); // 例如: 202507

			const query = `
		SELECT * FROM notes
		WHERE yyyymm = ?
		ORDER BY created_at ASC
	`;

			const result = await db.getAllAsync(query, [currentYYYYMM]);
			console.log(`${currentYYYYMM} 月份的 notes 表记录数量:`, result.length);

			// 将数据库数据转换为DiaryData格式
			const transformedData: DiaryData = {};

			result.forEach((note: any) => {
				// 将数据库日期格式转换为YYYY-MM-DD格式
				const noteDate = new Date(note.created_at);
				const dateKey = noteDate.toISOString().split('T')[0]; // 获取YYYY-MM-DD格式

				// 移除HTML标签，获取纯文本内容预览
				const textContent = note.content.replace(/<[^>]*>/g, '');
				const contentPreview = textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent;

				const diaryEntry: DiaryEntry = {
					id: note.id.toString(),
					timestamp: note.created_at,
					content: contentPreview,
					mood_img_name: note.mood_app_in_img_name || 'app_in_mood1.png'
				};

				// 如果该日期还没有记录，创建一个新数组
				if (!transformedData[dateKey]) {
					transformedData[dateKey] = [];
				}

				// 添加记录到对应日期
				transformedData[dateKey].push(diaryEntry);
			});

			setDiaryData(transformedData);
			//console.log('转换后的日记数据:', transformedData);
		} catch (error) {
			console.error('主页查询 notes 表时出错:', error);
		} finally {
			setIsLoading(false);
		}
	};




	const handlePress = () => {
		// 旋转动画
		Animated.timing(rotateAnim, {
			toValue: showMoodPicker ? 0 : 1,
			duration: 300,
			useNativeDriver: true,
		}).start();

		// 如果正在关闭心情选择器，清除选中的日期
		if (showMoodPicker) {
			setSelectedDate(undefined);
			setGlobalSelectedDate('');
		}

		setShowMoodPicker(!showMoodPicker);
	};

	// 创建旋转插值
	const rotation = rotateAnim.interpolate({
		inputRange: [0, 1],
		outputRange: ['0deg', '45deg']
	});

	// 生成整个月的日期数组
	const generateMonthDays = () => {
		const now = new Date();
		const year = now.getFullYear();
		const month = now.getMonth(); // 0-based month
		const daysInMonth = new Date(year, month + 1, 0).getDate();

		const days = [];
		for (let day = 1; day <= daysInMonth; day++) {
			const dateKey = `${year}-${(month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
			days.push({
				day,
				dateKey,
				hasData: diaryData[dateKey] && diaryData[dateKey].length > 0,
				entries: diaryData[dateKey] || []
			});
		}
		return days;
	};



	// 添加跳转到某天笔记列表的函数
	const handleDayPress = (dateKey: string, hasData: boolean) => {
		if (hasData) {
			// 解析日期字符串 (YYYY-MM-DD)
			const [year, month, day] = dateKey.split('-');

			// 跳转到详情页面并传递参数
			router.push({
				pathname: '/indexsomedaynotelist',
				params: {
					year: year,
					month: month,
					day: day
				}
			});
		} else {
			// 如果当天没有日记，打开心情选择器并保存选中的日期
			console.log('选中了没有日记的日期:', dateKey);
			setSelectedDate(dateKey);
			setGlobalSelectedDate(dateKey);
			setShowMoodPicker(true);

			// 旋转动画
			Animated.timing(rotateAnim, {
				toValue: 1,
				duration: 300,
				useNativeDriver: true,
			}).start();
		}
	};



	return (
		<>
			<StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
			{/* <ImageBackground
				source={require('../assets/images/bg4.png')}
				style={styles.backgroundImage}
			> */}
			<View style={{
				flex: 1,
				paddingTop: insets.top,
				// paddingBottom: insets.bottom,
				backgroundColor: '#fff',
			}}>
				{/* <StatusBar backgroundColor="#f40" barStyle="dark-content" /> */}

				<View style={styles.safecontainer}>
					<View style={styles.container}>
						<View style={styles.headrow}>
							<TouchableOpacity >
								<HugeiconsIcon
									icon={GlobalIcon}
									size={25}
									color="#333"
								/>
							</TouchableOpacity>

							<Text style={styles.headrowtext}> {monthNames[month - 1]} </Text>

							<TouchableOpacity onPress={() => router.push('/settings')} >
								<HugeiconsIcon
									icon={Settings03Icon}
									size={25}
									color="#333"
								/>
							</TouchableOpacity>
						</View>

						{/* 日记数据显示 */}
						<ScrollView style={styles.diaryContainer}>
							{isLoading ? (
								<View style={styles.loadingContainer}>
									{/* <Text style={styles.loadingText}>正在加载...</Text> */}
								</View>
							) : Object.keys(diaryData).length === 0 ? (
								<View style={styles.moodGrid}>
									{generateMonthDays().map(({ day, dateKey, hasData, entries }) => (
										<TouchableOpacity
											key={dateKey}
											style={styles.moodGridItemNoData}
											onPress={() => handleDayPress(dateKey, false)}
										>
											<View style={styles.emptyCell}>
												<Text style={styles.emptyDateText}>
													{day}
												</Text>
											</View>
										</TouchableOpacity>
									))}
								</View>
							) : (
								<View style={styles.moodGrid}>
									{generateMonthDays().map(({ day, dateKey, hasData, entries }) => (
										<TouchableOpacity
											key={dateKey}
											style={hasData ? styles.moodGridItemClickable : styles.moodGridItemNoData}
											onPress={() => handleDayPress(dateKey, hasData)}
										>
											{hasData ? (
												<View style={styles.moodItemContainer}>
													{entries.length > 1 ? (
														// 多个心情记录时使用动画
														<Animated.Image
															source={getMoodImage(getCurrentEntry(dateKey, entries).mood_img_name)}
															style={[
																styles.moodGridImage,
																{
																	transform: [
																		{
																			translateX: animatedValues.current[dateKey] ?
																				animatedValues.current[dateKey].interpolate({
																					inputRange: [-1, 0, 1],
																					outputRange: [-80, 0, 80], // 从左边(-80)滑到中心(0)再到右边(80)
																				}) : 0
																		}
																	],
																	opacity: animatedValues.current[dateKey] ?
																		animatedValues.current[dateKey].interpolate({
																			inputRange: [-1, -0.5, 0, 0.5, 1],
																			outputRange: [0, 1, 1, 1, 0], // 滑入时淡入，滑出时淡出
																		}) : 1
																}
															]}
														/>
													) : (
														// 单个心情记录时使用普通图片
														<Image
															source={getMoodImage(getCurrentEntry(dateKey, entries).mood_img_name)}
															style={styles.moodGridImage}
														/>
													)}


												</View>
											) : (
												<View style={styles.emptyCell}>
													<Text style={styles.emptyDateText}>
														{day}
													</Text>
												</View>
											)}

											{entries.length > 1 && (
												<View style={styles.multiMoodIndicator}>
													<Text style={styles.multiMoodText}>{entries.length}</Text>
												</View>
											)}
										</TouchableOpacity>
									))}
								</View>
							)}
						</ScrollView>

						{/* <HugeiconsIcon
							icon={CircleIcon}
							size={60}
							color="#00000010"
							strokeWidth={1.0}
							style={styles.home_bottom_add}
						/> */}

					</View>



					{/* Mood Picker */}
					{
						showMoodPicker &&
						<View style={styles.mood_picker_view}>
							<MoodCircle
								showMoodPicker={showMoodPicker}
							/>
						</View>
					}



					{/* Add Button */}
					<View style={styles.home_bottom_add_view}>
						<TouchableOpacity onPress={handlePress}>
							{/* <Animated.Image
									style={[
										styles.home_bottom_add,
										{ transform: [{ rotate: rotation }] }
									]}
									source={require('../assets/images/icon/me_icon_add.png')}
								/> */}
							<Animated.View style={{ transform: [{ rotate: rotation }] }}>
								<HugeiconsIcon
									icon={PlusSignCircleIcon}
									size={70}
									color="#000000dd"
									strokeWidth={1.0}
									style={styles.home_bottom_add}
								/>
							</Animated.View>
						</TouchableOpacity>




						{/* <Link href="/details">
									<Text style={styles.link}> details </Text>
								</Link> */}

						{/* <Link href="/demopage">
									<Text style={styles.link}> demo_page </Text>
								</Link> */}

						{/* <Link href="/testsql">
									<Text style={styles.link}> test sql </Text>
								</Link> */}

						{/* <Link href="/notes">
									<Text style={styles.link}> notes </Text>
								</Link> */}



						{/* <View style={{ flexDirection: 'row', marginTop: 10 }}>
								<Link href="/sqladd">
									<Text style={styles.link}> sqladd </Text>
								</Link>

								<Link href="/sqlshow">
									<Text style={styles.link}> sqlshow </Text>
								</Link>
							</View> */}



						{/* <View style={{ flexDirection: 'row', marginTop: 10 }}>
							<Link href="/insertimg">
								<Text style={styles.link}> insert img </Text>
							</Link>

							<Link href="/queryNotes">
								<Text style={styles.link}> query notes </Text>
							</Link>
						</View> */}

						{/* <Link href="/z-dev-fontfamily">
							<Text style={styles.link}> dev font family </Text>
						</Link> */}



	

						<Link href="/z-dev-sql-notes" style={{ position: 'absolute', bottom: 150, right: 10 }}>
							<Text style={styles.link}> dev sql notes </Text>
						</Link>


						<Link href="/z-dev-get-mongodb-notes-by-yyyymm" style={{ position: 'absolute', bottom: 100, right: 10 }}>
							<Text style={styles.link}> dev get mongodb notes by yyyymm </Text>
						</Link>

					</View>
				</View>
			</View>
			{/* </ImageBackground> */}
		</>
	);
}

const styles = StyleSheet.create({
	safecontainer: {
		flex: 1,
	},
	container: {
		flex: 1,
	},
	headrow: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',

		paddingHorizontal: 20,
		paddingVertical: 10,
	},
	headrowtext: {
		// flex: 1,
		textAlign: 'center',
		fontSize: 30,

		// fontFamily: 'Brushzing-Regular',
		// fontFamily: 'DatangStory-drxBK',
		// fontFamily: 'CuteAurora-PK3lZ',
		// fontFamily: 'DatangStory-drxBK',
		// fontFamily: 'Biscuitos',
		fontFamily: 'Whitenice',

		color: '#5D4E37',
		// color: '#fff',
		// backgroundColor: '#07c160',
	},

	// 日记数据显示样式
	diaryContainer: {
		flex: 1,
		paddingHorizontal: 10,
		marginTop: 10,
	},

	emptyCell: {
		width: 60,
		height: 60,
		borderRadius: 30,
		borderWidth: 1,
		borderColor: '#e0e0e0',
		borderStyle: 'dashed',
		justifyContent: 'center',
		alignItems: 'center',
	},

	emptyDateText: {
		fontSize: 16,
		fontWeight: 'bold',
		color: '#999',
		textAlign: 'center',
	},

	home_bottom_add_view: {
		flex: 1,
		position: 'absolute',
		left: 0,
		right: 0,
		bottom: 25,
		alignItems: 'center',
		justifyContent: 'center',
	},
	home_bottom_add: {
		width: 88,
		height: 88,
	},

	mood_picker_view: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		justifyContent: 'center',
		alignItems: 'center',
	},

	link: {
		color: '#8B6E3F',
		fontSize: 20,
		textDecorationLine: 'none',
		margin: 10,
	},
	moodGrid: {
		flexDirection: 'row',
		flexWrap: 'wrap',
		justifyContent: 'space-between',
	},
	moodGridItemClickable: {
		width: '19%', // 一行5个，每个占19%宽度
		marginBottom: 15,
		alignItems: 'center',
		opacity: 1,
	},
	moodGridItemNoData: {
		width: '19%', // 一行5个，每个占19%宽度
		marginBottom: 15,
		alignItems: 'center',
		opacity: 1,
	},
	moodGridImage: {
		width: 60,
		height: 60,
		borderRadius: 30, // 圆形图片
	},
	moodGridText: {
		marginTop: 8,
		fontSize: 12,
		color: '#666',
		textAlign: 'center',
		lineHeight: 14,
	},
	loadingContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		paddingTop: 50,
	},
	moodItemContainer: {
		position: 'relative',
		overflow: 'hidden',
		borderRadius: 30,
		width: 60,
		height: 60,
	},

	multiMoodIndicator: {
		position: 'absolute',
		top: 0,
		right: 4,
		backgroundColor: '#ffffff',
		borderRadius: 10,
		paddingHorizontal: 4,
		paddingVertical: 2,
		minWidth: 20,
		minHeight: 20,
		justifyContent: 'center',
		alignItems: 'center',
		zIndex: 999,

		// shadowColor: '#000',
		// shadowOffset: {
		// 	width: 0,
		// 	height: 1,
		// },
		// shadowOpacity: 0.3,
		// shadowRadius: 2,
		// elevation: 10,
	},
	multiMoodText: {
		fontSize: 10,
		fontWeight: 'bold',
		color: '#000',
		textAlign: 'center',
	},
});