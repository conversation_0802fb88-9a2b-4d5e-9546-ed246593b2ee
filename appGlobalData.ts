import { ImageSourcePropType } from 'react-native';

import { Storage } from 'expo-sqlite/kv-store';


// 全局选中的心情信息
export let globalSelectedMood = {
    moodIndex: -1,
    moodName: '',
    selectedDate: '',
    timestamp: 0
};

// 更新全局选中心情的函数
export const setGlobalSelectedMood = (moodIndex: number, moodName: string, selectedDate?: string) => {
    globalSelectedMood = {
        moodIndex,
        moodName,
        selectedDate: selectedDate || globalSelectedMood.selectedDate,
        timestamp: Date.now()
    };
    console.log('全局心情状态已更新:', globalSelectedMood);
};

// 设置全局选中日期的函数
export const setGlobalSelectedDate = (selectedDate: string) => {
    globalSelectedMood.selectedDate = selectedDate;
    console.log('全局选中日期已更新:', selectedDate);
};

// 获取全局选中心情的函数
export const getGlobalSelectedMood = () => {
    return globalSelectedMood;
};

// 清空全局选中心情的函数
export const clearGlobalSelectedMood = () => {
    globalSelectedMood = {
        moodIndex: -1,
        moodName: '',
        selectedDate: '',
        timestamp: 0
    };
};

// app 内置心情图片
export const moods = [
    { name: 'app_in_mood1.png', image: require('./assets/images/mood/app_in_mood1.png') },
    { name: 'app_in_mood2.png', image: require('./assets/images/mood/app_in_mood2.png') },
    { name: 'app_in_mood3.png', image: require('./assets/images/mood/app_in_mood3.png') },
    { name: 'app_in_mood4.png', image: require('./assets/images/mood/app_in_mood4.png') },
    { name: 'app_in_mood5.png', image: require('./assets/images/mood/app_in_mood5.png') },
    { name: 'app_in_mood6.png', image: require('./assets/images/mood/app_in_mood6.png') },
    { name: 'app_in_mood7.png', image: require('./assets/images/mood/app_in_mood7.png') },
    { name: 'app_in_mood8.png', image: require('./assets/images/mood/app_in_mood8.png') },
    { name: 'app_in_mood9.png', image: require('./assets/images/mood/app_in_mood9.png') },
    { name: 'app_in_mood10.png', image: require('./assets/images/mood/app_in_mood10.png') }
];

// 根据心情图片名称获取对应的图片资源
export const getMoodImage = (moodImgName: string) => {
    const moodItem = moods.find(mood => mood.name === moodImgName);
    return moodItem ? moodItem.image : moods[0].image; // 如果找不到，返回默认的第一个心情图片
};

// 其他全局数据可以在这里添加
// 例如：
// export const appColors = { ... };
// export const appSettings = { ... };
// 等等

// 默认导出整个数据对象，方便一次性导入所有数据
const globalData = {
    moods,
    globalSelectedMood,
    setGlobalSelectedMood,
    setGlobalSelectedDate,
    getGlobalSelectedMood,
    clearGlobalSelectedMood
};

export default globalData;