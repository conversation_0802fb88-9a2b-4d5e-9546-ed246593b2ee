# 超简化版 HTTP 客户端使用说明

## 主要改进
- 自动添加 token（从 expo-sqlite/kv-store 获取）
- 401 错误时自动清除过期 token
- 增加超时时间到 10 秒
- 直接返回 response.data

## 基本用法

```typescript
import httpClient from '../axios/axiosApi';

// GET 请求
const data = await httpClient.get('/myip');

// POST 请求（自动添加 token，如果有的话）
const result = await httpClient.post('/x_mooood_note', {
  do_sth: 'get_my_notes_by_yyyymm',
  yyyymm: 202412,
});

// 登录请求（没有 token 也不会报错）
const loginResult = await httpClient.post('/x_mooood_user', {
  do_sth: 'denglu',
  name: 'username',
  password: 'password',
});
```

## 迁移示例

### 原来的 fetch 方式：
```typescript
const token = await Storage.getItem('userToken');
const response = await fetch(env.BASE_URL + '/x_mooood_note', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  },
  body: JSON.stringify(data),
});
const result = await response.json();
```

### 现在的方式：
```typescript
const result = await httpClient.post('/x_mooood_note', data);
```

就这么简单！token 会自动添加（如果存在），不需要手动处理。
