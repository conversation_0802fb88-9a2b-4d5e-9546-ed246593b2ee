import axios from 'axios';
import env from '../env';
import { Storage } from 'expo-sqlite/kv-store';

const instance = axios.create({
  baseURL: env.BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 自动添加token
instance.interceptors.request.use(
  async (config: any) => {
    try {
      const token = await Storage.getItem('userToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.warn('获取token失败:', error);
    }
    return config;
  },
  error => Promise.reject(error)
);

// 响应拦截器
instance.interceptors.response.use(
  response => response.data,
  async error => {
    // 401错误自动清除token
    if (error.response?.status === 401) {
      try {
        await Storage.removeItem('userToken');
        console.log('Token已清除，需要重新登录');
      } catch (e) {
        console.error('清除token失败:', e);
      }
    }
    return Promise.reject(error);
  }
);

export default instance;
