# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- **Start development server**: `npx expo start`
- **Start with clear prebuild**: `npx expo prebuild --clean`
- **Run tests**: `npm test`
- **Run linter**: `npm run lint`

### Platform-Specific Commands
- **iOS simulator**: `npx expo run:ios`
- **iOS iPhone 6S**: `npx expo run:ios --device "iPhone 6S"`
- **iOS iPhone 13 mini**: `npx expo run:ios --device "zqm 13 mini"`
- **Android emulator**: `npx expo run:android`

### Build Commands
- **Android release build**: `cd android && ./gradlew assembleRelease`
- **Install Android APK**: `adb install android/app/build/outputs/apk/release/app-release.apk`

## Architecture Overview

### Project Structure
This is a React Native Expo application with file-based routing using Expo Router. The app is a mood tracking diary application with rich text editing capabilities.

### Key Architecture Components

#### Database Layer
- **Local Database**: SQLite using `expo-sqlite`
- **Cloud Database**: MongoDB and node.js
- **KV Store**: Uses `expo-sqlite/kv-store` for user authentication tokens and settings

#### Core Data Models
- **Mood Interface**: Defined in `appGlobalData.ts` with name and image properties
- **Notes Table**: Stores rich text content with mood associations and timestamps

#### Navigation & Routing
- **Expo Router**: File-based routing with pages in the `app/` directory
- **Main Pages**: index.tsx (mood selection), editnote.tsx (rich text editor), notes.tsx (note listing), settings.tsx

#### Rich Text Editing
- **Library**: `@10play/tentap-editor` for rich text editing
- **Features**: Bold, italic, underline, strikethrough, headings, task lists, code blocks, undo/redo
- **Storage**: Content saved as HTML in both local SQLite and cloud database

#### Authentication
- **Global Auth Utils**: Centralized in `appGlobalData.ts`
- **Token Management**: JWT tokens stored in KV store
- **User State**: Login status and user info persistence

#### UI Components
- **Mood Selection**: Visual mood picker with custom images
- **Rich Text Editor**: Full-featured editor with toolbar
- **Date Selection**: Calendar-based note creation for specific dates
- **Safe Area Handling**: Proper insets using `react-native-safe-area-context`

### Key Dependencies
- **React Native**: 0.76.9
- **Expo**: ~52.0.43
- **Rich Text**: @10play/tentap-editor
- **Database**: expo-sqlite
- **Navigation**: expo-router
- **Icons**: @hugeicons/react-native
- **Animations**: react-native-reanimated

### Development Notes
- The app supports both local SQLite storage and cloud synchronization
- Rich text content is stored as HTML for cross-platform compatibility
- Mood data is statically defined in `appGlobalData.ts` with corresponding image assets
- Authentication state is managed through KV store for persistence
- The app uses TypeScript with strict mode enabled